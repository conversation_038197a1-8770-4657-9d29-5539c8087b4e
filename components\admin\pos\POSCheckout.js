import { useState } from 'react'
import CustomerInfoForm from './CustomerInfoForm'
import PaymentMethodSelector from './PaymentMethodSelector'
import POSSquarePayment from './POSSquarePayment'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * POSCheckout component for handling the complete checkout process
 *
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service
 * @param {Object} props.tier - Selected pricing tier
 * @param {Function} props.onBack - Callback to go back
 * @param {Function} props.onComplete - Callback when transaction is complete
 * @returns {JSX.Element}
 */
export default function POSCheckout({ service, tier, onBack, onComplete }) {
  const [checkoutStep, setCheckoutStep] = useState('customer') // 'customer', 'payment', 'processing'
  const [customerData, setCustomerData] = useState(null)
  const [paymentMethod, setPaymentMethod] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  // Calculate total amount
  const totalAmount = parseFloat(tier?.price || 0)

  // Format duration
  const formatDuration = (minutes) => {
    if (!minutes) return 'Duration not specified'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours === 0) return `${mins} minutes`
    if (mins === 0) return `${hours} hour${hours > 1 ? 's' : ''}`
    return `${hours}h ${mins}m`
  }

  const handleCustomerData = (data) => {
    setCustomerData(data)
    setCheckoutStep('payment')
  }

  const handlePaymentMethodSelect = (method) => {
    setPaymentMethod(method)
    if (method === 'cash') {
      // For cash payments, process immediately
      processCashPayment()
    }
    // For card payments, the Square component will handle the flow
  }

  const processCashPayment = async () => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create booking and payment record
      const result = await createBookingAndPayment('cash')

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to process cash payment')
      }
    } catch (err) {
      console.error('Cash payment error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentSuccess = async (paymentResult) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create booking and payment record with Square transaction details
      const result = await createBookingAndPayment('card', paymentResult)

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to record card payment')
      }
    } catch (err) {
      console.error('Card payment completion error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentError = (error) => {
    console.error('Square payment error:', error)
    setError(error.message || 'Card payment failed')
  }

  const createBookingAndPayment = async (paymentMethod, paymentDetails = null) => {
    try {
      console.log('🔄 Creating POS booking...')

      // Get authentication token from Supabase session (simple approach)
      const { supabase } = await import('@/lib/supabase')

      // Get current session without forcing refresh
      const { data: { session }, error } = await supabase.auth.getSession()

      console.log('Booking authentication check:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasToken: !!session?.access_token,
        userEmail: session?.user?.email,
        error: error?.message
      })

      if (!session?.access_token) {
        const errorMsg = error?.message || 'No active session'
        console.error('❌ Booking authentication failed:', errorMsg)
        throw new Error(`Authentication required for booking creation. Please refresh the page and log in again. (${errorMsg})`)
      }

      console.log('✅ Booking authentication successful for user:', session.user?.email)

      const response = await fetch('/api/admin/pos/create-booking', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          service: {
            id: service.id,
            name: service.name
          },
          tier: {
            id: tier.id,
            name: tier.name,
            duration: tier.duration,
            price: tier.price
          },
          customer: customerData,
          payment: {
            method: paymentMethod,
            amount: totalAmount,
            currency: 'AUD',
            details: paymentDetails
          }
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create booking')
      }

      return data
    } catch (error) {
      console.error('Booking creation error:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  const renderOrderSummary = () => (
    <div className={styles.orderSummary}>
      <h4>Order Summary</h4>
      <div className={styles.summaryItem}>
        <span className={styles.serviceName}>
          {safeRender(service.name, 'Service')}
        </span>
        <span className={styles.tierName}>
          {safeRender(tier.name, 'Tier')} - {formatDuration(tier.duration)}
        </span>
        <span className={styles.price}>
          ${totalAmount.toFixed(2)}
        </span>
      </div>
      <div className={styles.summaryTotal}>
        <strong>Total: ${totalAmount.toFixed(2)} AUD</strong>
      </div>
    </div>
  )

  return (
    <div className={styles.checkoutContainer}>
      <div className={styles.checkoutHeader}>
        <h2>Checkout</h2>
        {renderOrderSummary()}
      </div>

      {error && (
        <div className={styles.checkoutError}>
          <span className={styles.errorIcon}>⚠️</span>
          {error}
          <button
            onClick={() => setError(null)}
            className={styles.dismissError}
          >
            ×
          </button>
        </div>
      )}

      <div className={styles.checkoutContent}>
        {checkoutStep === 'customer' && (
          <CustomerInfoForm
            onCustomerData={handleCustomerData}
            isLoading={isProcessing}
          />
        )}

        {checkoutStep === 'payment' && !paymentMethod && (
          <PaymentMethodSelector
            onPaymentMethodSelect={handlePaymentMethodSelect}
            amount={totalAmount}
            isLoading={isProcessing}
          />
        )}

        {checkoutStep === 'payment' && paymentMethod === 'cash' && isProcessing && (
          <div className={styles.processingCash}>
            <div className={styles.processingIcon}>💵</div>
            <h3>Processing Cash Payment</h3>
            <p>Recording transaction...</p>
            <div className={styles.loadingSpinner}></div>
          </div>
        )}

        {checkoutStep === 'payment' && paymentMethod === 'card' && (
          <POSSquarePayment
            amount={totalAmount}
            currency="AUD"
            onSuccess={handleSquarePaymentSuccess}
            onError={handleSquarePaymentError}
            orderDetails={{
              service: service.name,
              tier: tier.name,
              customer: customerData?.name || 'Walk-in Customer'
            }}
          />
        )}
      </div>

      <div className={styles.checkoutNavigation}>
        {checkoutStep === 'customer' && (
          <button
            className={styles.backButton}
            onClick={onBack}
            disabled={isProcessing}
          >
            ← Back to Duration
          </button>
        )}

        {checkoutStep === 'payment' && !isProcessing && (
          <button
            className={styles.backButton}
            onClick={() => setCheckoutStep('customer')}
          >
            ← Back to Customer Info
          </button>
        )}
      </div>
    </div>
  )
}
