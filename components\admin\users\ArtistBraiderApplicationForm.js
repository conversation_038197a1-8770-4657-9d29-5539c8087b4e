import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import styles from '@/styles/admin/users/ArtistBraiderApplicationForm.module.css'

export default function ArtistBraiderApplicationForm({ onSubmit, onCancel, initialData = null }) {
  const { user } = useAuth()
  const [formData, setFormData] = useState({
    application_type: 'artist',
    experience_years: '',
    portfolio_url: '',
    availability_preferences: {
      weekdays: [],
      weekends: false,
      evenings: false,
      flexible: false
    },
    service_specializations: [],
    previous_experience: '',
    references: ''
  })
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({})

  // Service specialization options
  const serviceOptions = {
    artist: [
      'Makeup Application',
      'Eyebrow Styling',
      'Eyelash Extensions',
      'Facial Treatments',
      'Nail Art',
      'Special Event Makeup',
      'Bridal Makeup',
      'Photography Makeup'
    ],
    braider: [
      'Box Braids',
      'Cornrows',
      'French Braids',
      'Dutch Braids',
      'Fishtail Braids',
      'Twist Braids',
      'Protective Styles',
      'Hair Extensions',
      'Wedding Hairstyles',
      'Special Occasion Styles'
    ]
  }

  const weekdayOptions = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'
  ]

  useEffect(() => {
    if (initialData) {
      setFormData(initialData)
    }
  }, [initialData])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    
    if (name.startsWith('availability_')) {
      const availabilityField = name.replace('availability_', '')
      setFormData(prev => ({
        ...prev,
        availability_preferences: {
          ...prev.availability_preferences,
          [availabilityField]: type === 'checkbox' ? checked : value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }))
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleWeekdayChange = (day) => {
    setFormData(prev => ({
      ...prev,
      availability_preferences: {
        ...prev.availability_preferences,
        weekdays: prev.availability_preferences.weekdays.includes(day)
          ? prev.availability_preferences.weekdays.filter(d => d !== day)
          : [...prev.availability_preferences.weekdays, day]
      }
    }))
  }

  const handleSpecializationChange = (specialization) => {
    setFormData(prev => ({
      ...prev,
      service_specializations: prev.service_specializations.includes(specialization)
        ? prev.service_specializations.filter(s => s !== specialization)
        : [...prev.service_specializations, specialization]
    }))
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.application_type) {
      newErrors.application_type = 'Application type is required'
    }

    if (!formData.experience_years || formData.experience_years < 0) {
      newErrors.experience_years = 'Please enter valid years of experience'
    }

    if (formData.portfolio_url && !isValidUrl(formData.portfolio_url)) {
      newErrors.portfolio_url = 'Please enter a valid URL'
    }

    if (formData.service_specializations.length === 0) {
      newErrors.service_specializations = 'Please select at least one specialization'
    }

    if (!formData.previous_experience || formData.previous_experience.trim().length < 50) {
      newErrors.previous_experience = 'Please provide at least 50 characters describing your experience'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (string) => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      await onSubmit({
        ...formData,
        user_id: user.id
      })
    } catch (error) {
      console.error('Error submitting application:', error)
    } finally {
      setLoading(false)
    }
  }

  const currentSpecializations = serviceOptions[formData.application_type] || []

  return (
    <div className={styles.formContainer}>
      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.header}>
          <h2>Artist/Braider Application</h2>
          <p>Please fill out this form to apply for a position on our team.</p>
        </div>

        <div className={styles.formGroup}>
          <label>Application Type *</label>
          <div className={styles.radioGroup}>
            <label className={styles.radioLabel}>
              <input
                type="radio"
                name="application_type"
                value="artist"
                checked={formData.application_type === 'artist'}
                onChange={handleChange}
              />
              Beauty Artist
            </label>
            <label className={styles.radioLabel}>
              <input
                type="radio"
                name="application_type"
                value="braider"
                checked={formData.application_type === 'braider'}
                onChange={handleChange}
              />
              Hair Braider
            </label>
          </div>
          {errors.application_type && <div className={styles.error}>{errors.application_type}</div>}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="experience_years">Years of Experience *</label>
          <input
            type="number"
            id="experience_years"
            name="experience_years"
            value={formData.experience_years}
            onChange={handleChange}
            className={styles.input}
            min="0"
            max="50"
            required
          />
          {errors.experience_years && <div className={styles.error}>{errors.experience_years}</div>}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="portfolio_url">Portfolio URL</label>
          <input
            type="url"
            id="portfolio_url"
            name="portfolio_url"
            value={formData.portfolio_url}
            onChange={handleChange}
            className={styles.input}
            placeholder="https://your-portfolio.com"
          />
          {errors.portfolio_url && <div className={styles.error}>{errors.portfolio_url}</div>}
        </div>

        <div className={styles.formGroup}>
          <label>Service Specializations *</label>
          <div className={styles.checkboxGrid}>
            {currentSpecializations.map(specialization => (
              <label key={specialization} className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  checked={formData.service_specializations.includes(specialization)}
                  onChange={() => handleSpecializationChange(specialization)}
                />
                {specialization}
              </label>
            ))}
          </div>
          {errors.service_specializations && <div className={styles.error}>{errors.service_specializations}</div>}
        </div>

        <div className={styles.formGroup}>
          <label>Availability Preferences</label>
          
          <div className={styles.availabilitySection}>
            <h4>Weekdays</h4>
            <div className={styles.checkboxGrid}>
              {weekdayOptions.map(day => (
                <label key={day} className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.availability_preferences.weekdays.includes(day)}
                    onChange={() => handleWeekdayChange(day)}
                  />
                  {day}
                </label>
              ))}
            </div>
          </div>

          <div className={styles.availabilityOptions}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="availability_weekends"
                checked={formData.availability_preferences.weekends}
                onChange={handleChange}
              />
              Available weekends
            </label>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="availability_evenings"
                checked={formData.availability_preferences.evenings}
                onChange={handleChange}
              />
              Available evenings (after 6 PM)
            </label>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="availability_flexible"
                checked={formData.availability_preferences.flexible}
                onChange={handleChange}
              />
              Flexible schedule
            </label>
          </div>
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="previous_experience">Previous Experience *</label>
          <textarea
            id="previous_experience"
            name="previous_experience"
            value={formData.previous_experience}
            onChange={handleChange}
            className={styles.textarea}
            rows="4"
            placeholder="Please describe your previous experience, training, and qualifications..."
            required
          />
          <div className={styles.charCount}>
            {formData.previous_experience.length} characters (minimum 50)
          </div>
          {errors.previous_experience && <div className={styles.error}>{errors.previous_experience}</div>}
        </div>

        <div className={styles.formGroup}>
          <label htmlFor="references">References</label>
          <textarea
            id="references"
            name="references"
            value={formData.references}
            onChange={handleChange}
            className={styles.textarea}
            rows="3"
            placeholder="Please provide contact information for professional references..."
          />
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            className={styles.cancelButton}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitButton}
            disabled={loading}
          >
            {loading ? 'Submitting...' : 'Submit Application'}
          </button>
        </div>
      </form>
    </div>
  )
}
