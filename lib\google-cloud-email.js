import nodemailer from 'nodemailer';

/**
 * Email Service for Google Cloud Integration
 * Supports Gmail SMTP, Google Workspace SMTP, and Gmail API
 */

/**
 * Create Gmail SMTP transporter
 * @returns {Object} Nodemailer transporter
 */
export const createGmailTransporter = () => {
  const config = {
    host: process.env.GMAIL_SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.GMAIL_SMTP_PORT) || 587,
    secure: process.env.GMAIL_SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.GMAIL_SMTP_USER,
      pass: process.env.GMAIL_SMTP_APP_PASSWORD
    },    tls: {
      rejectUnauthorized: false // For development/testing
    }
  };

  return nodemailer.createTransport(config);
};

/**
 * Create Google Workspace SMTP transporter
 * @returns {Object} Nodemailer transporter  
 */
export const createWorkspaceTransporter = () => {
  const config = {
    host: process.env.WORKSPACE_SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.WORKSPACE_SMTP_PORT) || 587,
    secure: process.env.WORKSPACE_SMTP_SECURE === 'true',
    auth: {
      user: process.env.WORKSPACE_SMTP_USER,
      pass: process.env.WORKSPACE_SMTP_APP_PASSWORD
    },    tls: {
      rejectUnauthorized: false
    }
  };

  return nodemailer.createTransport(config);
};

/**
 * Send email using Gmail SMTP
 * @param {Object} params - Email parameters
 * @param {string} params.to - Recipient email
 * @param {string} params.subject - Email subject
 * @param {string} params.text - Plain text body
 * @param {string} params.html - HTML body (optional)
 * @param {string} params.from - From email (optional, uses env default)
 * @param {string} params.fromName - From name (optional, uses env default)
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendGmailEmail = async ({ 
  to, 
  subject, 
  text, 
  html, 
  from, 
  fromName 
}) => {
  try {
    // Validate required environment variables
    if (!process.env.GMAIL_SMTP_USER || !process.env.GMAIL_SMTP_APP_PASSWORD) {
      throw new Error('Gmail SMTP credentials not configured. Please set GMAIL_SMTP_USER and GMAIL_SMTP_APP_PASSWORD in environment variables.');
    }

    const transporter = createGmailTransporter();
    
    const fromEmail = from || process.env.GMAIL_FROM_EMAIL || process.env.GMAIL_SMTP_USER;
    const senderName = fromName || process.env.GMAIL_FROM_NAME || 'Ocean Soul Sparkles';
    
    const mailOptions = {
      from: `"${senderName}" <${fromEmail}>`,
      to,
      subject,
      text,
      html: html || text
    };

    const result = await transporter.sendMail(mailOptions);
    
    return {
      success: true,
      messageId: result.messageId,
      service: 'gmail-smtp',
      recipient: to
    };
  } catch (error) {
    console.error('Error sending Gmail SMTP email:', error);
    return {
      success: false,
      error: error.message,
      service: 'gmail-smtp'
    };
  }
};

/**
 * Send email using Google Workspace SMTP
 * @param {Object} params - Email parameters
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendWorkspaceEmail = async ({ 
  to, 
  subject, 
  text, 
  html, 
  from, 
  fromName 
}) => {
  try {
    // Validate required environment variables
    if (!process.env.WORKSPACE_SMTP_USER || !process.env.WORKSPACE_SMTP_APP_PASSWORD) {
      throw new Error('Google Workspace SMTP credentials not configured. Please set WORKSPACE_SMTP_USER and WORKSPACE_SMTP_APP_PASSWORD in environment variables.');
    }

    const transporter = createWorkspaceTransporter();
    
    const fromEmail = from || process.env.WORKSPACE_FROM_EMAIL || process.env.WORKSPACE_SMTP_USER;
    const senderName = fromName || process.env.WORKSPACE_FROM_NAME || 'Ocean Soul Sparkles';
    
    const mailOptions = {
      from: `"${senderName}" <${fromEmail}>`,
      to,
      subject,
      text,
      html: html || text
    };

    const result = await transporter.sendMail(mailOptions);
    
    return {
      success: true,
      messageId: result.messageId,
      service: 'workspace-smtp',
      recipient: to
    };
  } catch (error) {
    console.error('Error sending Workspace SMTP email:', error);
    return {
      success: false,
      error: error.message,
      service: 'workspace-smtp'
    };
  }
};

/**
 * Test Gmail SMTP connection
 * @returns {Promise<Object>} - Result of the connection test
 */
export const testGmailConnection = async () => {
  try {
    if (!process.env.GMAIL_SMTP_USER || !process.env.GMAIL_SMTP_APP_PASSWORD) {
      return {
        success: false,
        message: 'Gmail SMTP credentials not configured',
        details: {
          missing: ['GMAIL_SMTP_USER', 'GMAIL_SMTP_APP_PASSWORD'].filter(
            key => !process.env[key]
          )
        }
      };
    }

    const transporter = createGmailTransporter();
    await transporter.verify();

    return {
      success: true,
      message: 'Gmail SMTP connection successful',
      details: {
        host: process.env.GMAIL_SMTP_HOST,
        port: process.env.GMAIL_SMTP_PORT,
        user: process.env.GMAIL_SMTP_USER
      }
    };
  } catch (error) {
    return {
      success: false,
      message: 'Gmail SMTP connection failed',
      details: { error: error.message }
    };
  }
};

/**
 * Test Google Workspace SMTP connection
 * @returns {Promise<Object>} - Result of the connection test
 */
export const testWorkspaceConnection = async () => {
  try {
    if (!process.env.WORKSPACE_SMTP_USER || !process.env.WORKSPACE_SMTP_APP_PASSWORD) {
      return {
        success: false,
        message: 'Google Workspace SMTP credentials not configured',
        details: {
          missing: ['WORKSPACE_SMTP_USER', 'WORKSPACE_SMTP_APP_PASSWORD'].filter(
            key => !process.env[key]
          )
        }
      };
    }

    const transporter = createWorkspaceTransporter();
    await transporter.verify();

    return {
      success: true,
      message: 'Google Workspace SMTP connection successful',
      details: {
        host: process.env.WORKSPACE_SMTP_HOST,
        port: process.env.WORKSPACE_SMTP_PORT,
        user: process.env.WORKSPACE_SMTP_USER
      }
    };
  } catch (error) {
    return {
      success: false,
      message: 'Google Workspace SMTP connection failed',
      details: { error: error.message }
    };
  }
};

/**
 * Universal email sender that chooses the best available service
 * Priority: Google Workspace SMTP > Gmail SMTP > OneSignal
 * @param {Object} params - Email parameters
 * @returns {Promise<Object>} - Result of the operation
 */
export const sendEmail = async (params) => {
  const { to, subject, text, html, from, fromName } = params;

  // Try Google Workspace SMTP first (if configured)
  if (process.env.WORKSPACE_SMTP_USER && process.env.WORKSPACE_SMTP_APP_PASSWORD) {
    console.log('Attempting to send email via Google Workspace SMTP...');
    const result = await sendWorkspaceEmail(params);
    if (result.success) {
      return result;
    }
    console.log('Google Workspace SMTP failed, trying Gmail SMTP...');
  }

  // Try Gmail SMTP second (if configured)
  if (process.env.GMAIL_SMTP_USER && process.env.GMAIL_SMTP_APP_PASSWORD) {
    console.log('Attempting to send email via Gmail SMTP...');
    const result = await sendGmailEmail(params);
    if (result.success) {
      return result;
    }
    console.log('Gmail SMTP failed, falling back to OneSignal...');
  }

  // Fall back to OneSignal (existing implementation)
  try {
    const { sendOneSignalEmail } = await import('./notifications');
    console.log('Attempting to send email via OneSignal...');
    return await sendOneSignalEmail({
      email: to,
      subject,
      message: text,
      htmlBody: html,
      data: { fallback: 'google-cloud-failed' }
    });
  } catch (error) {
    console.error('All email services failed:', error);
    return {
      success: false,
      error: 'All email services failed',
      details: error.message
    };
  }
};

/**
 * Get email service status
 * @returns {Promise<Object>} - Status of all email services
 */
export const getEmailServiceStatus = async () => {
  const status = {
    services: {},
    primary: null,
    available: []
  };

  // Test Google Workspace SMTP
  if (process.env.WORKSPACE_SMTP_USER && process.env.WORKSPACE_SMTP_APP_PASSWORD) {
    const workspaceTest = await testWorkspaceConnection();
    status.services.workspace = workspaceTest;
    if (workspaceTest.success) {
      status.available.push('workspace');
      if (!status.primary) status.primary = 'workspace';
    }
  }

  // Test Gmail SMTP
  if (process.env.GMAIL_SMTP_USER && process.env.GMAIL_SMTP_APP_PASSWORD) {
    const gmailTest = await testGmailConnection();
    status.services.gmail = gmailTest;
    if (gmailTest.success) {
      status.available.push('gmail');
      if (!status.primary) status.primary = 'gmail';
    }
  }

  // Check OneSignal availability
  if (process.env.ONESIGNAL_REST_API_KEY && process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID) {
    status.services.onesignal = {
      success: true,
      message: 'OneSignal configured',
      details: { configured: true }
    };
    status.available.push('onesignal');
    if (!status.primary) status.primary = 'onesignal';
  }

  return status;
};

export default {
  sendEmail,
  sendGmailEmail,
  sendWorkspaceEmail,
  testGmailConnection,
  testWorkspaceConnection,
  getEmailServiceStatus
};
