import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import UserList from '@/components/admin/users/UserList'
import UserManagementDashboard from '@/components/admin/users/UserManagementDashboard'
import { useAuth } from '@/contexts/AuthContext'
import styles from '@/styles/admin/UsersPage.module.css'

export default function UsersPage() {
  const router = useRouter()
  const { hasAdminAccess } = useAuth()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('dashboard')

  // Handle new user button click
  const handleNewUser = () => {
    router.push('/admin/users/new')
  }

  const handleApplications = () => {
    router.push('/admin/users/applications')
  }

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'users', label: 'All Users', icon: '👥' },
    { id: 'applications', label: 'Applications', icon: '📝' }
  ]

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="User Management">
        <div className={styles.usersPage}>
          <div className={styles.header}>
            <h2>User Management</h2>
            <div className={styles.actions}>
              <button
                className={styles.actionButton}
                onClick={handleApplications}
              >
                📝 Applications
              </button>
              <button
                className={styles.addButton}
                onClick={handleNewUser}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 5v14M5 12h14"></path>
                </svg>
                Add New User
              </button>
            </div>
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          {/* Tab Navigation */}
          <div className={styles.tabNavigation}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`${styles.tab} ${activeTab === tab.id ? styles.activeTab : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className={styles.tabIcon}>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className={styles.tabContent}>
            {activeTab === 'dashboard' && <UserManagementDashboard />}
            {activeTab === 'users' && <UserList />}
            {activeTab === 'applications' && (
              <div className={styles.comingSoon}>
                <h3>Applications Management</h3>
                <p>Artist and Braider application review system coming soon...</p>
                <button
                  className={styles.actionButton}
                  onClick={handleApplications}
                >
                  View Applications Page
                </button>
              </div>
            )}
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
