import { getAdminClient, getCurrentUserFromRequest } from '@/lib/supabase';
import { sendEmail, getEmailServiceStatus } from '@/lib/google-cloud-email';

/**
 * API endpoint for Google Cloud email management
 * Handles testing, configuration, and status checking
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check authentication
  try {
    const { user, role } = await getCurrentUserFromRequest(req);
    if (!user || role !== 'admin') {
      return res.status(401).json({ error: 'Unauthorized' });
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' });
  }

  const { action, ...params } = req.body;

  try {
    switch (action) {
      case 'test-email':
        return await handleTestEmail(req, res, params);
      case 'send-test-email':
        return await handleSendTestEmail(req, res, params);
      case 'get-status':
        return await handleGetStatus(req, res);
      case 'update-config':
        return await handleUpdateConfig(req, res, params);
      default:
        return res.status(400).json({ 
          error: 'Invalid action',
          available_actions: ['test-email', 'send-test-email', 'get-status', 'update-config']
        });
    }
  } catch (error) {
    console.error('Google Cloud email API error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

/**
 * Test email configuration
 */
async function handleTestEmail(req, res, params) {
  const { service = 'auto' } = params;

  try {
    let result;

    if (service === 'gmail') {
      const { testGmailConnection } = await import('@/lib/google-cloud-email');
      result = await testGmailConnection();
    } else if (service === 'workspace') {
      const { testWorkspaceConnection } = await import('@/lib/google-cloud-email');
      result = await testWorkspaceConnection();
    } else {
      // Test all available services
      result = await getEmailServiceStatus();
    }

    return res.status(200).json(result);
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Send a test email
 */
async function handleSendTestEmail(req, res, params) {
  const { to, subject = 'Test Email from Ocean Soul Sparkles', message = 'This is a test email to verify your email configuration is working correctly.' } = params;

  if (!to) {
    return res.status(400).json({
      error: 'Recipient email address is required'
    });
  }

  try {
    const result = await sendEmail({
      to,
      subject,
      text: message,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #6e8efb;">Test Email - Ocean Soul Sparkles</h2>
          <p>This is a test email to verify your Google Cloud email configuration is working correctly.</p>
          <p><strong>Configuration Test Results:</strong></p>
          <ul>
            <li>✅ Email service is properly configured</li>
            <li>✅ SMTP connection successful</li>
            <li>✅ Email delivery working</li>
          </ul>
          <p>If you received this email, your Google Cloud email integration is working perfectly!</p>
          <hr>
          <p style="color: #666; font-size: 0.9em;">
            This email was sent from your Ocean Soul Sparkles admin panel as a configuration test.
          </p>
        </div>
      `
    });

    return res.status(200).json({
      success: result.success,
      message: result.success 
        ? `Test email sent successfully via ${result.service || 'email service'}` 
        : 'Failed to send test email',
      details: result,
      recipient: to
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message,
      recipient: to
    });
  }
}

/**
 * Get email service status
 */
async function handleGetStatus(req, res) {
  try {
    const status = await getEmailServiceStatus();
    
    return res.status(200).json({
      success: true,
      ...status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message,
      services: {},
      primary: null,
      available: []
    });
  }
}

/**
 * Update email configuration in database
 */
async function handleUpdateConfig(req, res, params) {
  const { 
    gmail_smtp_user,
    gmail_smtp_app_password,
    workspace_smtp_user,
    workspace_smtp_app_password,
    email_from_name,
    enable_email_notifications
  } = params;

  try {
    const adminClient = getAdminClient();
    if (!adminClient) {
      throw new Error('Database connection failed');
    }

    // Prepare settings to update
    const settings = {};
    
    if (gmail_smtp_user !== undefined) settings.gmail_smtp_user = gmail_smtp_user;
    if (gmail_smtp_app_password !== undefined) settings.gmail_smtp_app_password = gmail_smtp_app_password;
    if (workspace_smtp_user !== undefined) settings.workspace_smtp_user = workspace_smtp_user;
    if (workspace_smtp_app_password !== undefined) settings.workspace_smtp_app_password = workspace_smtp_app_password;
    if (email_from_name !== undefined) settings.email_from_name = email_from_name;
    if (enable_email_notifications !== undefined) settings.enable_email_notifications = enable_email_notifications;

    // Update each setting
    const updatePromises = Object.entries(settings).map(([key, value]) =>
      adminClient
        .from('admin_settings')
        .upsert([{ setting_key: key, setting_value: value }], {
          onConflict: 'setting_key'
        })
    );

    const results = await Promise.all(updatePromises);
    
    // Check for errors
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      throw new Error(`Failed to update settings: ${errors.map(e => e.error.message).join(', ')}`);
    }

    return res.status(200).json({
      success: true,
      message: 'Email configuration updated successfully',
      updated_settings: Object.keys(settings)
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}
