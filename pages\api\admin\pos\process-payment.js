import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * API endpoint for processing Square payments in POS terminal
 * This endpoint handles Square payment processing and returns transaction details
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] POS Process Payment API called: ${req.method}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  // Authenticate request
  const authResult = await authenticateAdminRequest(req)
  const { authorized, error, user, role } = authResult

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, error?.message || 'Unknown error')
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed',
      requestId
    })
  }

  console.log(`[${requestId}] Authentication successful. User: ${user?.email}, Role: ${role}`)

  try {
    const {
      sourceId,
      token,
      amountMoney,
      amount,
      currency = 'AUD',
      orderDetails = {},
      billingContact
    } = req.body

    // Support both new and legacy request formats
    const paymentToken = sourceId || token
    const paymentAmount = amountMoney?.amount ? amountMoney.amount / 100 : amount
    const paymentCurrency = amountMoney?.currency || currency

    // Validate required data
    if (!paymentToken || !paymentAmount) {
      return res.status(400).json({
        error: 'Missing required data',
        message: 'Payment token and amount are required'
      })
    }

    console.log(`[${requestId}] Processing Square payment for amount: ${paymentAmount} ${paymentCurrency}`)

    // Check if Square SDK is configured
    const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
    const squareApplicationId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
    const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'

    if (!squareAccessToken || !squareApplicationId || !squareLocationId) {
      console.error(`[${requestId}] Square configuration missing:`, {
        hasAccessToken: !!squareAccessToken,
        hasApplicationId: !!squareApplicationId,
        hasLocationId: !!squareLocationId
      })

      return res.status(500).json({
        error: 'Payment system not configured',
        message: 'Square payment processing is not properly configured. Please check environment variables.'
      })
    }

    console.log(`[${requestId}] Square configuration found, processing payment...`)

    try {
      // Process payment using Square API
      const paymentResult = await processSquarePayment(
        paymentToken,
        paymentAmount,
        paymentCurrency,
        squareAccessToken,
        squareLocationId,
        squareEnvironment,
        orderDetails,
        billingContact
      )

      if (paymentResult.success) {
        console.log(`[${requestId}] Square payment successful: ${paymentResult.paymentId}`)

        return res.status(200).json({
          success: true,
          paymentId: paymentResult.paymentId,
          transactionId: paymentResult.transactionId,
          amount: paymentAmount,
          currency: paymentCurrency,
          status: 'COMPLETED',
          receiptUrl: paymentResult.receiptUrl,
          message: 'Payment processed successfully'
        })
      } else {
        throw new Error(paymentResult.error || 'Payment processing failed')
      }

    } catch (paymentError) {
      console.error(`[${requestId}] Square payment processing error:`, paymentError)

      return res.status(400).json({
        success: false,
        error: 'Payment processing failed',
        message: paymentError.message || 'Unable to process payment',
        requestId
      })
    }

  } catch (error) {
    console.error(`[${requestId}] POS Payment Processing Error:`, error)

    return res.status(500).json({
      success: false,
      error: 'Payment processing error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing payment',
      requestId,
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * Simulate Square payment processing for development/demo purposes
 * In production, this would be replaced with actual Square API calls
 */
async function simulateSquarePayment(token, amount, currency, orderDetails) {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Simulate random success/failure (90% success rate)
  const isSuccess = Math.random() > 0.1

  if (isSuccess) {
    return {
      success: true,
      paymentId: `sq_payment_${Date.now()}`,
      transactionId: `sq_txn_${Math.random().toString(36).substring(2, 15)}`,
      receiptUrl: `https://squareup.com/receipt/preview/${Math.random().toString(36).substring(2, 15)}`
    }
  } else {
    return {
      success: false,
      error: 'Card declined - insufficient funds'
    }
  }
}

/**
 * Validate Square payment token format
 */
function validateSquareToken(token) {
  // Square tokens typically start with specific prefixes
  const validPrefixes = ['cnon:', 'ccof:', 'sq0idp-', 'sq0ids-']
  const hasValidPrefix = validPrefixes.some(prefix => token.startsWith(prefix))

  if (!hasValidPrefix) {
    console.warn('Square token does not have expected prefix:', token.substring(0, 10) + '...')
  }

  return {
    isValid: token && token.length > 10,
    hasValidPrefix,
    tokenType: token.substring(0, 6),
    length: token.length
  }
}

/**
 * Process Square payment using fetch API (since we can't use Square SDK on server)
 * This makes direct API calls to Square's payments endpoint
 */
async function processSquarePayment(token, amount, currency, accessToken, locationId, environment, orderDetails, billingContact) {
  try {
    // Validate token format
    const tokenValidation = validateSquareToken(token)
    console.log('Square token validation:', tokenValidation)

    if (!tokenValidation.isValid) {
      throw new Error('Invalid Square payment token format')
    }

    // Square API endpoint
    const baseUrl = environment === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com'

    const url = `${baseUrl}/v2/payments`

    // Generate idempotency key for payment
    const idempotencyKey = `pos_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

    // Prepare payment request with sandbox-specific configuration
    const paymentRequest = {
      source_id: token,
      idempotency_key: idempotencyKey,
      amount_money: {
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toUpperCase()
      },
      location_id: locationId,
      note: `POS Terminal Payment - ${orderDetails.service || 'Service'}`
    }

    // Add billing contact for AVS verification if provided
    if (billingContact) {
      paymentRequest.billing_address = {
        address_line_1: billingContact.addressLines?.[0] || billingContact.addressLine1,
        address_line_2: billingContact.addressLines?.[1] || billingContact.addressLine2,
        locality: billingContact.locality,
        administrative_district_level_1: billingContact.administrativeDistrictLevel1,
        postal_code: billingContact.postalCode,
        country: billingContact.country
      }
      console.log('Including billing address for AVS verification:', paymentRequest.billing_address)
    }

    // For sandbox environment, add verification bypass options
    if (environment === 'sandbox') {
      paymentRequest.accept_partial_authorization = false
      paymentRequest.autocomplete = true
      console.log('Sandbox mode: Adding verification bypass options')
    }

    console.log('Making Square API request:', {
      url,
      amount: paymentRequest.amount_money.amount,
      currency: paymentRequest.amount_money.currency,
      locationId,
      environment,
      tokenLength: token.length,
      idempotencyKey
    })

    // Make API request to Square with enhanced error handling
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
        'User-Agent': 'OceanSoulSparkles-POS/1.0'
      },
      body: JSON.stringify(paymentRequest)
    })

    const responseData = await response.json()

    console.log('Square API response status:', response.status)
    console.log('Square API response data:', JSON.stringify(responseData, null, 2))

    if (!response.ok) {
      console.error('Square API error response:', {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      })

      // Handle specific Square error types
      if (responseData.errors && responseData.errors.length > 0) {
        const error = responseData.errors[0]
        let errorMessage = error.detail || error.code || 'Payment failed'

        // Provide user-friendly error messages
        switch (error.code) {
          case 'CARD_DECLINED':
            errorMessage = 'Card was declined. Please try a different payment method.'
            break
          case 'INSUFFICIENT_FUNDS':
            errorMessage = 'Insufficient funds. Please try a different card.'
            break
          case 'INVALID_CARD':
            errorMessage = 'Invalid card information. Please check your card details.'
            break
          case 'EXPIRED_CARD':
            errorMessage = 'Card has expired. Please use a different card.'
            break
          case 'CVV_FAILURE':
            errorMessage = 'CVV verification failed. Please check your security code.'
            break
          case 'ADDRESS_VERIFICATION_FAILURE':
            if (environment === 'sandbox') {
              errorMessage = 'Address verification failed in test mode. This is expected with test cards. In production, ensure billing address matches the card.'
            } else {
              errorMessage = 'Address verification failed. Please verify your billing address matches your card.'
            }
            break
          case 'GENERIC_DECLINE':
            errorMessage = 'Payment was declined. Please try a different payment method.'
            break
          case 'INVALID_EXPIRATION':
            errorMessage = 'Invalid card expiration date. Please check your card details.'
            break
          default:
            errorMessage = error.detail || 'Payment processing failed'
        }

        return {
          success: false,
          error: errorMessage
        }
      }

      return {
        success: false,
        error: 'Payment processing failed'
      }
    }

    // Check payment status and handle AVS failures
    const payment = responseData.payment
    console.log('Square payment response:', {
      id: payment.id,
      status: payment.status,
      cardDetails: payment.card_details
    })

    // Handle failed payments due to AVS or other issues
    if (payment.status === 'FAILED') {
      const cardDetails = payment.card_details || {}
      const avsStatus = cardDetails.avs_status
      const cvvStatus = cardDetails.cvv_status

      console.log('Payment failed with details:', {
        avsStatus,
        cvvStatus,
        cardDetails
      })

      let failureReason = 'Payment failed'

      if (avsStatus === 'AVS_REJECTED') {
        if (environment === 'sandbox') {
          failureReason = 'Address verification failed in sandbox mode. This is normal for test cards. Use the pre-filled billing address or try a different test card.'
        } else {
          failureReason = 'Address verification failed. Please ensure your billing address exactly matches your card statement.'
        }
      } else if (cvvStatus === 'CVV_REJECTED') {
        failureReason = 'Security code verification failed. Please check your CVV.'
      }

      return {
        success: false,
        error: failureReason,
        paymentId: payment.id,
        status: payment.status,
        avsStatus,
        cvvStatus
      }
    }

    // Payment successful
    console.log('Square payment successful:', payment.id)

    return {
      success: true,
      paymentId: payment.id,
      transactionId: payment.id,
      receiptUrl: payment.receipt_url || null,
      status: payment.status
    }

  } catch (error) {
    console.error('Square payment processing error:', error)
    return {
      success: false,
      error: error.message || 'Network error during payment processing'
    }
  }
}
