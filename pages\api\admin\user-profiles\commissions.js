import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

export default async function handler(req, res) {
  try {
    // Check for development auth bypass first
    const isDevelopment = process.env.NODE_ENV === 'development'
    const authBypass = process.env.ENABLE_AUTH_BYPASS === 'true'

    let user = null
    let userRole = null

    if (isDevelopment && authBypass) {
      console.log('User Profiles Commissions API: Development auth bypass enabled')
      // In development with auth bypass, assume dev role
      userRole = 'dev'
    } else {
      // Authenticate request using our robust auth module
      const { authorized, error, user: authUser, role } = await authenticateAdminRequest(req)

      if (!authorized) {
        console.error('User Profiles Commissions API: Authentication failed:', error?.message || 'Unknown error')
        return res.status(401).json({
          error: 'Unauthorized access',
          message: error?.message || 'Authentication failed'
        })
      }

      console.log('User Profiles Commissions API: Authentication successful. User:', authUser?.email, 'Role:', role)

      user = authUser
      userRole = role

      // Only DEV and Admin users can access this endpoint
      if (!['dev', 'admin'].includes(userRole)) {
        return res.status(403).json({ error: 'Access denied. Admin privileges required.' })
      }
    }

    // Get admin client to bypass RLS policies
    const supabase = getAdminClient()
    if (!supabase) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    if (req.method === 'GET') {
      // Get all commission rates with user information
      const { data: commissionRates, error: ratesError } = await supabase
        .from('user_commission_rates')
        .select(`
          *,
          user_profiles!inner(
            full_name,
            email
          ),
          user_roles!inner(
            role
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (ratesError) {
        console.error('Error fetching commission rates:', ratesError)
        return res.status(500).json({ error: 'Failed to fetch commission rates' })
      }

      // Format the response
      const formattedRates = commissionRates.map(rate => ({
        id: rate.id,
        user_id: rate.user_id,
        user_name: rate.user_profiles?.full_name || 'Unknown User',
        user_email: rate.user_profiles?.email || '',
        user_role: rate.user_roles?.role || 'user',
        service_category: rate.service_category,
        commission_percentage: rate.commission_percentage,
        base_rate: rate.base_rate,
        bonus_rate: rate.bonus_rate,
        effective_from: rate.effective_from,
        effective_until: rate.effective_until,
        is_active: rate.is_active,
        notes: rate.notes,
        created_at: rate.created_at,
        updated_at: rate.updated_at
      }))

      // Also fetch users who can have commission rates (artists and braiders)
      const { data: eligibleUsers, error: usersError } = await supabase
        .from('user_roles')
        .select(`
          id,
          role,
          users:id (
            email,
            raw_user_meta_data
          )
        `)
        .in('role', ['artist', 'braider'])

      const formattedUsers = eligibleUsers?.map(userRole => ({
        id: userRole.id,
        name: userRole.users?.raw_user_meta_data?.name || userRole.users?.email || 'Unknown User',
        email: userRole.users?.email || '',
        role: userRole.role
      })) || []

      return res.status(200).json({
        success: true,
        commissionRates: formattedRates,
        users: formattedUsers
      })

    } else if (req.method === 'POST') {
      // Create new commission rate
      const {
        user_id,
        service_category,
        commission_percentage,
        base_rate,
        bonus_rate,
        effective_from,
        effective_until,
        notes
      } = req.body

      // Validate required fields
      if (!user_id || !service_category || commission_percentage === undefined) {
        return res.status(400).json({ error: 'Missing required fields: user_id, service_category, commission_percentage' })
      }

      // Validate commission percentage
      if (commission_percentage < 0 || commission_percentage > 100) {
        return res.status(400).json({ error: 'Commission percentage must be between 0 and 100' })
      }

      // Verify the target user exists and has appropriate role
      const { data: targetUser, error: targetUserError } = await supabase
        .from('user_roles')
        .select('role')
        .eq('id', user_id)
        .single()

      if (targetUserError || !targetUser) {
        return res.status(400).json({ error: 'Target user not found' })
      }

      if (!['artist', 'braider'].includes(targetUser.role)) {
        return res.status(400).json({ error: 'Commission rates can only be set for artists and braiders' })
      }

      try {
        // Insert new commission rate
        const { data: newRate, error: insertError } = await supabase
          .from('user_commission_rates')
          .insert({
            user_id,
            service_category,
            commission_percentage: parseFloat(commission_percentage),
            base_rate: base_rate ? parseFloat(base_rate) : null,
            bonus_rate: bonus_rate ? parseFloat(bonus_rate) : 0,
            effective_from: effective_from || new Date().toISOString().split('T')[0],
            effective_until,
            notes,
            created_by: user.id,
            is_active: true
          })
          .select()
          .single()

        if (insertError) {
          console.error('Error creating commission rate:', insertError)
          return res.status(500).json({ error: 'Failed to create commission rate' })
        }

        // Log the activity
        await supabase
          .from('user_activity_logs')
          .insert({
            user_id: user.id,
            action: 'create_commission_rate',
            details: `Created commission rate for user ${user_id}: ${commission_percentage}% for ${service_category}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          })

        return res.status(201).json({
          success: true,
          commissionRate: newRate,
          message: 'Commission rate created successfully'
        })

      } catch (error) {
        console.error('Error creating commission rate:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }

    } else if (req.method === 'PUT') {
      // Update existing commission rate
      const {
        id,
        commission_percentage,
        base_rate,
        bonus_rate,
        effective_from,
        effective_until,
        notes,
        is_active
      } = req.body

      if (!id) {
        return res.status(400).json({ error: 'Commission rate ID is required' })
      }

      try {
        // Update commission rate
        const { data: updatedRate, error: updateError } = await supabase
          .from('user_commission_rates')
          .update({
            commission_percentage: commission_percentage !== undefined ? parseFloat(commission_percentage) : undefined,
            base_rate: base_rate !== undefined ? (base_rate ? parseFloat(base_rate) : null) : undefined,
            bonus_rate: bonus_rate !== undefined ? parseFloat(bonus_rate) : undefined,
            effective_from,
            effective_until,
            notes,
            is_active,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)
          .select()
          .single()

        if (updateError) {
          console.error('Error updating commission rate:', updateError)
          return res.status(500).json({ error: 'Failed to update commission rate' })
        }

        // Log the activity
        await supabase
          .from('user_activity_logs')
          .insert({
            user_id: user.id,
            action: 'update_commission_rate',
            details: `Updated commission rate ${id}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          })

        return res.status(200).json({
          success: true,
          commissionRate: updatedRate,
          message: 'Commission rate updated successfully'
        })

      } catch (error) {
        console.error('Error updating commission rate:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }

    } else if (req.method === 'DELETE') {
      // Delete commission rate
      const { id } = req.body

      if (!id) {
        return res.status(400).json({ error: 'Commission rate ID is required' })
      }

      try {
        // Delete commission rate
        const { error: deleteError } = await supabase
          .from('user_commission_rates')
          .delete()
          .eq('id', id)

        if (deleteError) {
          console.error('Error deleting commission rate:', deleteError)
          return res.status(500).json({ error: 'Failed to delete commission rate' })
        }

        // Log the activity
        await supabase
          .from('user_activity_logs')
          .insert({
            user_id: user.id,
            action: 'delete_commission_rate',
            details: `Deleted commission rate ${id}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          })

        return res.status(200).json({
          success: true,
          message: 'Commission rate deleted successfully'
        })

      } catch (error) {
        console.error('Error deleting commission rate:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }

    } else {
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE'])
      return res.status(405).json({ error: 'Method not allowed' })
    }

  } catch (error) {
    console.error('API error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
