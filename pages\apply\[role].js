import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import { useAuth } from '@/contexts/AuthContext'
import ArtistBraiderApplicationForm from '@/components/admin/users/ArtistBraiderApplicationForm'
import styles from '@/styles/ApplicationPage.module.css'

export default function ApplicationPage() {
  const router = useRouter()
  const { role } = router.query
  const { user, loading: authLoading } = useAuth()
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const [error, setError] = useState(null)

  // Validate role parameter
  const validRoles = ['artist', 'braider']
  const isValidRole = validRoles.includes(role)

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!authLoading && !user) {
      router.push(`/login?redirect=${encodeURIComponent(router.asPath)}`)
    }
  }, [user, authLoading, router])

  const handleSubmit = async (applicationData) => {
    setSubmitting(true)
    setError(null)

    try {
      // Import the authenticated fetch utility
      const { apiFetch } = await import('@/lib/api-fetch')

      const result = await apiFetch('/api/applications/submit', {
        method: 'POST',
        body: JSON.stringify({
          ...applicationData,
          application_type: role
        })
      })

      setSubmitted(true)
      console.log('Application submitted successfully:', result)
    } catch (err) {
      console.error('Error submitting application:', err)
      setError(err.message)
    } finally {
      setSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.push('/')
  }

  // Show loading while checking auth
  if (authLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect if not authenticated (handled in useEffect)
  if (!user) {
    return null
  }

  // Show error for invalid role
  if (!isValidRole) {
    return (
      <div className={styles.container}>
        <Head>
          <title>Invalid Application Type - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.error}>
          <h1>Invalid Application Type</h1>
          <p>The application type "{role}" is not valid. Please use one of the following:</p>
          <ul>
            <li><a href="/apply/artist">Artist Application</a></li>
            <li><a href="/apply/braider">Braider Application</a></li>
          </ul>
        </div>
      </div>
    )
  }

  // Show success message after submission
  if (submitted) {
    return (
      <div className={styles.container}>
        <Head>
          <title>Application Submitted - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.success}>
          <div className={styles.successIcon}>✓</div>
          <h1>Application Submitted Successfully!</h1>
          <p>
            Thank you for your interest in joining Ocean Soul Sparkles as a {role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'}.
          </p>
          <p>
            Your application has been received and will be reviewed by our team. We'll contact you within 3-5 business days with next steps.
          </p>
          <div className={styles.nextSteps}>
            <h3>What happens next?</h3>
            <ol>
              <li>Our team will review your application and portfolio</li>
              <li>If selected, we'll schedule a skills assessment</li>
              <li>Final interview with our management team</li>
              <li>Welcome to the Ocean Soul Sparkles family!</li>
            </ol>
          </div>
          <button 
            onClick={() => router.push('/')}
            className={styles.homeButton}
          >
            Return to Home
          </button>
        </div>
      </div>
    )
  }

  const roleTitle = role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'

  return (
    <div className={styles.container}>
      <Head>
        <title>{roleTitle} Application - Ocean Soul Sparkles</title>
        <meta name="description" content={`Apply to join Ocean Soul Sparkles as a ${roleTitle}. Submit your application and portfolio for review.`} />
      </Head>

      <div className={styles.header}>
        <h1>Apply as a {roleTitle}</h1>
        <p>Join the Ocean Soul Sparkles team and showcase your talents in a supportive, creative environment.</p>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <strong>Error:</strong> {error}
        </div>
      )}

      <div className={styles.formContainer}>
        <ArtistBraiderApplicationForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          initialData={{ application_type: role }}
        />
      </div>

      {submitting && (
        <div className={styles.submittingOverlay}>
          <div className={styles.submittingModal}>
            <div className={styles.spinner}></div>
            <p>Submitting your application...</p>
          </div>
        </div>
      )}
    </div>
  )
}
