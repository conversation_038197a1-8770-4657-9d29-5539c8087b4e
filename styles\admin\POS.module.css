/* Enhanced POS Terminal Styles */
.posContainer {
  padding: 1rem;
  max-width: 1600px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
  background: #f8f9fa;
}

/* Enhanced Header with Dashboard Elements */
.posHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(78, 205, 196, 0.3);
  position: relative;
  overflow: hidden;
}

.posHeader::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.headerLeft {
  flex: 1;
}

.posTitle {
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.posSubtitle {
  font-size: 1.2rem;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.headerRight {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  z-index: 1;
}

.quickStats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.statItem {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.statLabel {
  font-size: 0.85rem;
  opacity: 0.9;
  margin: 0;
}

/* Step Indicator */
.stepIndicator {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1.5rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.step.active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.stepNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  font-weight: 600;
  font-size: 0.9rem;
}

.step.active .stepNumber {
  background: white;
  color: #4ECDC4;
}

.stepLabel {
  font-weight: 500;
  font-size: 0.9rem;
}

/* Content Area */
.posContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 500px;
  overflow: hidden;
}

/* Enhanced Service Grid */
.serviceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  padding: 2rem;
}

.serviceTile {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.serviceTile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4ECDC4, #44A08D);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.serviceTile:hover {
  border-color: #4ECDC4;
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.serviceTile:hover::before {
  transform: scaleX(1);
}

.serviceTile.selected {
  border-color: #4ECDC4;
  background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
  color: white;
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(78, 205, 196, 0.3);
}

.serviceTile.selected::before {
  transform: scaleX(1);
}

.serviceIcon {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  display: block;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.serviceName {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.serviceDescription {
  font-size: 0.95rem;
  opacity: 0.8;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.servicePriceRange {
  font-size: 1.2rem;
  font-weight: 700;
  color: #4ECDC4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.serviceTile.selected .servicePriceRange {
  color: white;
}

.tierCount {
  font-size: 0.85rem;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
  border: 1px solid #e9ecef;
}

.serviceTile.selected .tierCount {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.serviceAvailability {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.serviceAvailability.available {
  background: #4caf50;
}

.serviceAvailability.busy {
  background: #ff9800;
}

.serviceAvailability.unavailable {
  background: #f44336;
}

.noServices {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.noServices h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.noServices p {
  font-size: 1rem;
  line-height: 1.5;
}

/* Tier Selection */
.tierSelector {
  padding: 2rem;
}

.tierHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.tierServiceName {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.tierSubtitle {
  font-size: 1.1rem;
  color: #666;
}

.tierGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.tierCard {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.tierCard:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.tierCard.recommended {
  border-color: #4ECDC4;
  position: relative;
}

.tierCard.recommended::before {
  content: 'Recommended';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #4ECDC4;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tierName {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.tierDuration {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1rem;
}

.tierPrice {
  font-size: 2rem;
  font-weight: 700;
  color: #4ECDC4;
  margin-bottom: 1rem;
}

.tierDescription {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

/* Navigation Buttons */
.navigationButtons {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.backButton {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.backButton:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.continueButton {
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continueButton:hover {
  background: #40b0a8;
  transform: translateY(-1px);
}

.continueButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Loading and Error States */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error h3 {
  color: #dc3545;
  margin-bottom: 1rem;
}

.retryButton {
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background: #40b0a8;
}

/* Customer Form Styles */
.customerForm {
  padding: 2rem;
  max-width: 600px;
  margin: 0 auto;
}

.customerFormHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.customerFormHeader h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.customerFormHeader p {
  color: #666;
  font-size: 1rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.input {
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #4ECDC4;
}

.input.inputError {
  border-color: #dc3545;
}

.errorText {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.checkboxLabel {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.checkboxLabel:hover {
  background-color: #f8f9fa;
}

.checkbox {
  margin-top: 0.25rem;
}

.checkboxText {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #555;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  margin-top: 1rem;
}

.skipButton {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skipButton:hover {
  background: #5a6268;
}

.privacyNote {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
}

/* Payment Method Selector Styles */
.paymentMethodSelector {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.paymentHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.paymentHeader h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.totalAmount {
  font-size: 1.2rem;
  color: #666;
}

.amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4ECDC4;
}

.paymentMethods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.paymentMethod {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.paymentMethod:hover {
  border-color: var(--method-color, #4ECDC4);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.15);
}

.paymentMethod.selected {
  border-color: var(--method-color, #4ECDC4);
  background: linear-gradient(135deg, var(--method-color, #4ECDC4) 0%, rgba(78, 205, 196, 0.1) 100%);
}

.methodIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.methodContent {
  flex: 1;
  margin-bottom: 1rem;
}

.methodName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.methodDescription {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.methodFeatures {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature {
  font-size: 0.8rem;
  color: #555;
  margin-bottom: 0.25rem;
  text-align: left;
}

.methodSelector {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.radioButton {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radioButton.selected {
  border-color: var(--method-color, #4ECDC4);
}

.radioInner {
  width: 10px;
  height: 10px;
  background: var(--method-color, #4ECDC4);
  border-radius: 50%;
}

.cashInstructions,
.cardInstructions {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.instructionHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.instructionHeader h4 {
  margin: 0;
  color: #333;
}

.instructionIcon {
  font-size: 1.2rem;
}

.instructionList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.instructionList li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e0e0e0;
  color: #555;
}

.instructionList li:last-child {
  border-bottom: none;
}

.securityNote {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1.5rem;
}

.securityIcon {
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.securityText {
  font-size: 0.9rem;
  color: #2d5a2d;
  line-height: 1.4;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .posContainer {
    padding: 0.5rem;
  }

  .posTitle {
    font-size: 2rem;
  }

  .stepIndicator {
    gap: 1rem;
  }

  .stepLabel {
    display: none;
  }

  .serviceGrid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }

  .tierGrid {
    grid-template-columns: 1fr;
  }

  .navigationButtons {
    padding: 1rem;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .paymentMethods {
    grid-template-columns: 1fr;
  }
}

/* Checkout Styles */
.checkoutContainer {
  padding: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.checkoutHeader {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.checkoutHeader h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1rem;
}

.orderSummary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.orderSummary h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.serviceName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.tierName {
  font-size: 0.9rem;
  color: #666;
}

.price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #4ECDC4;
}

.summaryTotal {
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
  font-size: 1.3rem;
  color: #333;
}

.checkoutError {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.errorIcon {
  font-size: 1.2rem;
}

.dismissError {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #721c24;
}

.checkoutContent {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.checkoutNavigation {
  display: flex;
  justify-content: flex-start;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.processingCash {
  padding: 3rem 2rem;
  text-align: center;
}

.processingIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.processingCash h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.processingCash p {
  color: #666;
  margin-bottom: 2rem;
}

/* Square Payment Styles */
.squarePaymentContainer {
  padding: 2rem;
  max-width: 500px;
  margin: 0 auto;
}

.paymentFormHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.paymentFormHeader h4 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.paymentAmount {
  font-size: 1.1rem;
  color: #666;
}

.paymentAmount span {
  font-size: 1.3rem;
  font-weight: 700;
  color: #4ECDC4;
}

.paymentError {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.errorContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 1rem;
}

.errorText {
  flex: 1;
}

.retryButton {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retryButton:hover {
  background: #c82333;
}

.cardFormContainer {
  margin: 2rem 0;
}

.cardForm {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem;
  min-height: 100px;
  background: white;
  position: relative;
}

.cardFormPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  color: #666;
  font-style: italic;
}

.paymentActions {
  margin-top: 2rem;
}

.processPaymentButton {
  width: 100%;
  background: #4ECDC4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.processPaymentButton:hover:not(:disabled) {
  background: #40b0a8;
  transform: translateY(-1px);
}

.processPaymentButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.buttonSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.paymentSecurity {
  margin-top: 2rem;
  text-align: center;
}

.securityBadges {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.securityBadge {
  background: #e8f5e8;
  color: #2d5a2d;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.securityText {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

/* Additional Service Grid Styles */
.noServices {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem 2rem;
  color: #666;
}

.noServices h3 {
  color: #333;
  margin-bottom: 1rem;
}

.tierCount {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
  font-style: italic;
}

.serviceError {
  text-align: center;
  color: #dc3545;
}

.tierError {
  text-align: center;
  color: #dc3545;
}

.tierInfo {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

/* Billing Address Form Styles for Square AVS */
.billingAddressForm {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.billingAddressForm h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.addressGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.addressField {
  display: flex;
  flex-direction: column;
}

.addressField label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.addressField input,
.addressField select {
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out;
}

.addressField input:focus,
.addressField select:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.25);
}

.addressNote {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 12px;
  margin-top: 15px;
}

.addressNote p {
  margin: 0;
  font-size: 13px;
  color: #0066cc;
  line-height: 1.4;
}

/* Responsive adjustments for billing address */
@media (max-width: 768px) {
  .addressGrid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .billingAddressForm {
    padding: 15px;
    margin-top: 15px;
  }
}

/* Availability Dashboard Styles */
.availabilityDashboard {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.dashboardTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.refreshButton {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.availabilityGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.timeSlotView {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.timeSlotHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.timeSlotTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.dateSelector {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.dateInput {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.timeSlots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
}

.timeSlot {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.85rem;
  font-weight: 500;
}

.timeSlot.available {
  border-color: #4caf50;
  color: #4caf50;
}

.timeSlot.available:hover {
  background: #4caf50;
  color: white;
  transform: translateY(-2px);
}

.timeSlot.busy {
  border-color: #ff9800;
  color: #ff9800;
  cursor: not-allowed;
}

.timeSlot.unavailable {
  border-color: #f44336;
  color: #f44336;
  cursor: not-allowed;
  opacity: 0.6;
}

.timeSlot.selected {
  background: #4ECDC4;
  border-color: #4ECDC4;
  color: white;
  transform: translateY(-2px);
}

.quickActions {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.quickActionsTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 1.5rem 0;
}

.actionButton {
  width: 100%;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.95rem;
  font-weight: 500;
}

.actionButton:hover {
  border-color: #4ECDC4;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.2);
}

.actionIcon {
  font-size: 1.5rem;
  color: #4ECDC4;
}

.actionContent {
  flex: 1;
  text-align: left;
}

.actionTitle {
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.actionDescription {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

/* Customer Search Component */
.customerSearch {
  position: relative;
  margin-bottom: 1.5rem;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #4ECDC4;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 1.2rem;
}

.searchResults {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
}

.searchResult {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.searchResult:hover {
  background: #f8f9fa;
}

.searchResult:last-child {
  border-bottom: none;
}

.customerName {
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.customerDetails {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
}

/* Mobile Responsive Updates */
@media (max-width: 768px) {
  .posHeader {
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .headerRight {
    align-items: center;
    width: 100%;
  }

  .quickStats {
    justify-content: center;
    flex-wrap: wrap;
  }

  .availabilityGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .timeSlots {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .actionButton {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .actionIcon {
    font-size: 1.2rem;
  }
}

/* Selected Slot Info */
.selectedSlotInfo {
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.selectedSlotInfo h4 {
  margin: 0 0 0.75rem 0;
  color: #2d5a2d;
  font-size: 1.1rem;
}

.selectedSlotInfo p {
  margin: 0.25rem 0;
  color: #2d5a2d;
  font-size: 0.9rem;
}

.selectedSlotInfo strong {
  font-weight: 600;
}

/* Search Spinner */
.searchSpinner {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
}

.searchSpinner .loadingSpinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

/* Status Indicator */
.statusIndicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}
